# =============================================================================
# Ren'Py Project .gitignore
# =============================================================================

# -----------------------------------------------------------------------------
# Operating System Files
# -----------------------------------------------------------------------------

# macOS
.DS_Store
**/.DS_Store
.AppleDouble
.LSOverride
Icon?

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# -----------------------------------------------------------------------------
# Ren'Py Specific Files
# -----------------------------------------------------------------------------

# Compiled Ren'Py files
*.rpyc
*.rpymc
*.rpyb

# Ren'Py runtime and cache
game/cache/
game/saves/
persistent
log.txt
errors.txt
traceback.txt
*.save

# Ren'Py build outputs and distributions
*.app/
*.exe
*.zip
*.tar.bz2
*.dmg
*.pkg
*-dists/
tmp/

# Ren'Py Android build
*.apk
*.aab
rapt/
android.keystore

# Ren'Py iOS build
ios-*/

# Ren'Py Web build
web-*/

# Ren'Py launcher files
project.json

# -----------------------------------------------------------------------------
# Binary Assets (Audio and Images)
# -----------------------------------------------------------------------------

# Audio files in game/audio directory
game/audio/**/*.mp3
game/audio/**/*.ogg
game/audio/**/*.wav
game/audio/**/*.flac
game/audio/**/*.aac
game/audio/**/*.m4a
game/audio/**/*.wma

# Image files in game/images directory
game/images/**/*.jpg
game/images/**/*.jpeg
game/images/**/*.png
game/images/**/*.gif
game/images/**/*.bmp
game/images/**/*.tiff
game/images/**/*.tif
game/images/**/*.webp
game/images/**/*.svg
game/images/**/*.ico

# Video files in game/images directory (if any)
game/images/**/*.mp4
game/images/**/*.avi
game/images/**/*.mov
game/images/**/*.wmv
game/images/**/*.flv
game/images/**/*.webm
game/images/**/*.mkv
game/images/**/*.m4v
game/images/**/*.3gp
game/images/**/*.ogv

# -----------------------------------------------------------------------------
# Development and Editor Files
# -----------------------------------------------------------------------------

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject
*.sublime-project
*.sublime-workspace

# -----------------------------------------------------------------------------
# Python Files
# -----------------------------------------------------------------------------

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/
.env

# -----------------------------------------------------------------------------
# Backup and Temporary Files
# -----------------------------------------------------------------------------

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp

# -----------------------------------------------------------------------------
# Optional Game-Specific Directories
# -----------------------------------------------------------------------------

# Uncomment these if you want to ignore screenshots or movies
# game/screenshots/
# game/movies/