# characters.rpy
# Character definitions for The Alpine Affair

define n = Character(None, what_prefix="\"", what_suffix="\"")  # Narrator, no name

define markus = Character("<PERSON> Blackwood", color="#C0C0C0") # silver-gray
define sophia = Character("Detective <PERSON>", color="#D2691E") # auburn brown-orange
define wilson = Character("<PERSON>. <PERSON>", color="#2E8B57") # dark green
define countess = Character("<PERSON>", color="#B0C4DE") # pale steel blue
define thomas = Character("<PERSON> Du<PERSON>", color="#4682B4") # steel blue
define marie = Character("<PERSON> Du<PERSON>", color="#CD5C5C") # muted red
define elena = Character("<PERSON>", color="#8B0000") # deep red
