label scene8e_isabella_interview:

    $ asked_intro_i = asked_reason_i = asked_markus_i = asked_obs_i = False

    scene bg lounge_interview with dissolve
    show isabella neutral at center
    with fade

    sophia "Chef <PERSON>, may I ask you a few questions?"
    isabella "Yes, Detective. I will help as I can."

    label .menu_loop_i:
        menu:
            "Questions for Chef <PERSON>:"

            "Please, introduce yourself." if not asked_intro_i:
                sophia "Let us begin with introductions, Chef."
                isabella "<PERSON>, Michelin-starred chef. I was invited here for the winter season to oversee the cuisine."
                $ notebook.append("Clue: <PERSON>, Michelin-star chef, invited for the season.")
                $ asked_intro_i = True
                jump .menu_loop_i

            "Why are you here at the Grand Glacier Hotel?" if not asked_reason_i:
                sophia "And what brought you to this hotel, specifically?"
                isabella "The manager offered me a contract, a chance to bring attention to the hotel. It was a professional opportunity."
                $ notebook.append("Clue: <PERSON> says she came under contract to raise hotel prestige.")
                $ asked_reason_i = True
                jump .menu_loop_i

            "Did you see <PERSON> before the incident?" if not asked_markus_i:
                sophia "Did you cross paths with <PERSON>?"
                isabella "Yes… we spoke briefly. He tasted one of my dishes at an event months ago, and he mocked me cruelly. I tried to ignore him here."
                $ notebook.append("Clue: <PERSON> mocked <PERSON>’s cooking in the past; tension exists.")
                $ asked_markus_i = True
                jump .menu_loop_i

            "Have you noticed anything unusual this morning?" if not asked_obs_i:
                sophia "And this morning, Chef? Did you notice anything unusual?"
                isabella "In the kitchen I found a bottle of wine moved from its rack. No one on staff claimed responsibility."
                $ notebook.append("Clue: Isabella found a wine bottle moved suspiciously in the kitchen.")
                $ asked_obs_i = True
                jump .menu_loop_i

            "That is all, thank you Chef." if asked_intro_i and asked_reason_i and asked_markus_i and asked_obs_i:
                sophia "Thank you, Chef Chen. That will do."
                hide isabella with fade
                return
