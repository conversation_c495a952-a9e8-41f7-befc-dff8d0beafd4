# ================================
# Scene 9 – Interview Hub (Suspect Selector)
# Syberia-style: pick suspects in any order until all are interviewed
# ================================

# --- Global defaults (top-level) ---
# Track which suspects have been interviewed in Scene 9
default interview_done_wilson = False
default interview_done_countess = False
default interview_done_durants = False
default interview_done_elena = False
default interview_done_isabella = False

# Optional: a simple notes list already exists in your project
# default notebook = []   # (Declared in Wilson scene doc; keep a single declaration in one file)

label scene8_interview_hub:

    scene bg lounge_interview with dissolve
    with fade

    sophia "We will proceed calmly. I will speak with each of you in turn. Please be patient, and answer truthfully."

    # Main loop – keeps showing until player has interviewed everyone
    label .menu_loop:
        $ remaining = (not interview_done_wilson) + (not interview_done_countess) + (not interview_done_durants) + (not interview_done_elena) + (not interview_done_isabella)

        menu:
            "Whom will <PERSON> interview next? (Remaining: [remaining])":

                "Dr. <PERSON>" if not interview_done_wilson:
                    call scene8a_wilson_interview
                    $ interview_done_wilson = True
                    jump .menu_loop

                "Countess Von Schreiber" if not interview_done_countess:
                    call scene8b_countess_interview
                    $ interview_done_countess = True
                    jump .menu_loop

                "Thomas & Marie Durant" if not interview_done_durants:
                    call scene8c_durants_interview
                    $ interview_done_durants = True
                    jump .menu_loop

                "Elena Rossi (Manager)" if not interview_done_elena:
                    call scene8d_elena_interview
                    $ interview_done_elena = True
                    jump .menu_loop

                "Chef Isabella Chen" if not interview_done_isabella:
                    call scene8e_isabella_interview
                    $ interview_done_isabella = True
                    jump .menu_loop

                # (Optional convenience) Review current notes without leaving the hub
                "Review notebook ([len(notebook)] notes)":
                    $ renpy.notify("Notebook entries: %d" % len(notebook))
                    # You can swap this for a proper screen later
                    jump .menu_loop

                # This appears only when ALL interviews are complete
                "Conclude interviews for now." if interview_done_wilson and interview_done_countess and interview_done_durants and interview_done_elena and interview_done_isabella:
                    sophia "Thank you, everyone. That will suffice for now. Please remain available."
                    jump scene10_hook  # Next scene label – replace with your actual next label

# --- Helper: placeholder for the next scene ---
label scene10_hook:
    scene bg lounge_morning with dissolve
    sophia "There is something else I must see…"
    # e.g., a staff member summons Sophia to a new clue location
    return
