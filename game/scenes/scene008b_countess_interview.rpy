label scene8b_countess_interview:

    $ asked_intro_c = asked_reason_c = asked_markus_c = asked_obs_c = False

    scene bg lounge_interview with dissolve
    show countess neutral at center
    with fade

    sophia "Countess <PERSON>, would you allow me a few questions?"
    countess "If you must, Detective… though I dislike such scenes."

    label .menu_loop_c:
        menu:
            "Questions for the Countess:"

            "Please, introduce yourself." if not asked_intro_c:
                sophia "Perhaps we may start with introductions, Countess."
                countess "<PERSON>. I come from Vienna. I am a widow and I prefer to keep my affairs discreet."
                $ notebook.append("Clue: <PERSON>, widow from Vienna, values discretion.")
                $ asked_intro_c = True
                jump .menu_loop_c

            "Why are you staying at the hotel?" if not asked_reason_c:
                sophia "And what brings you here, to this remote mountain retreat?"
                countess "I came seeking quiet. The world below is… tiresome."
                $ notebook.append("Clue: <PERSON> claims she came seeking solitude.")
                $ asked_reason_c = True
                jump .menu_loop_c

            "Did you see <PERSON> recently?" if not asked_markus_c:
                sophia "Did you by chance speak with or observe <PERSON>?"
                countess "Only in passing. He was loud, brash… entirely without refinement."
                $ notebook.append("Clue: Countess dismisses <PERSON> as loud and unrefined.")
                $ asked_markus_c = <PERSON>
                jump .menu_loop_c

            "Did anything strike you as unusual?" if not asked_obs_c:
                sophia "Have you noticed anything peculiar these past hours?"
                countess "The staff seem… restless. And the storm is worse than they warned."
                $ notebook.append("Clue: Countess observed nervous staff and worsening storm.")
                $ asked_obs_c = True
                jump .menu_loop_c

            "That is all, thank you Countess." if asked_intro_c and asked_reason_c and asked_markus_c and asked_obs_c:
                sophia "I appreciate your candor, Countess. That will do for now."
                hide countess with fade
                return
